# Frontend Architecture Plan for Blog Website

## Technology Stack

### Core Framework
- **Next.js 14+** with App Router
- **React 18+** with Server Components
- **TypeScript** for type safety
- **Tailwind CSS** for styling
- **Shadcn/ui** for component library

### State Management
- **Zustand** for client-side state
- **React Query (TanStack Query)** for server state
- **React Hook Form** for form management

### Additional Libraries
- **next-intl** for internationalization (Hindi/English)
- **next-themes** for dark/light mode
- **framer-motion** for animations
- **react-markdown** for content rendering
- **lucide-react** for icons

## Project Structure

```
blog-frontend/
├── app/                          # Next.js App Router
│   ├── (auth)/                   # Auth route group
│   │   ├── login/
│   │   ├── register/
│   │   └── forgot-password/
│   ├── (blog)/                   # Main blog routes
│   │   ├── page.tsx              # Home page
│   │   ├── posts/
│   │   │   ├── [slug]/
│   │   │   └── page/[page]/
│   │   ├── category/
│   │   │   └── [slug]/
│   │   ├── tag/
│   │   │   └── [slug]/
│   │   └── search/
│   ├── (admin)/                  # Admin dashboard
│   │   ├── dashboard/
│   │   ├── posts/
│   │   ├── comments/
│   │   ├── media/
│   │   ├── users/
│   │   └── settings/
│   ├── api/                      # API routes (if needed)
│   ├── globals.css
│   ├── layout.tsx
│   └── loading.tsx
├── components/                   # Reusable components
│   ├── ui/                       # Base UI components
│   ├── blog/                     # Blog-specific components
│   ├── admin/                    # Admin components
│   ├── forms/                    # Form components
│   └── layout/                   # Layout components
├── lib/                          # Utility functions
│   ├── api.ts                    # API client
│   ├── auth.ts                   # Authentication utilities
│   ├── utils.ts                  # General utilities
│   └── validations.ts            # Form validations
├── hooks/                        # Custom React hooks
├── stores/                       # Zustand stores
├── types/                        # TypeScript type definitions
├── public/                       # Static assets
└── messages/                     # Internationalization files
    ├── en.json
    └── hi.json
```

## Component Architecture

### 1. Layout Components

#### MainLayout
```typescript
// components/layout/MainLayout.tsx
interface MainLayoutProps {
  children: React.ReactNode;
  showSidebar?: boolean;
}

export function MainLayout({ children, showSidebar = true }: MainLayoutProps) {
  return (
    <div className="min-h-screen bg-background">
      <Header />
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          <main className={showSidebar ? "lg:col-span-3" : "lg:col-span-4"}>
            {children}
          </main>
          {showSidebar && (
            <aside className="lg:col-span-1">
              <Sidebar />
            </aside>
          )}
        </div>
      </div>
      <Footer />
    </div>
  );
}
```

#### Header Component
```typescript
// components/layout/Header.tsx
export function Header() {
  return (
    <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container mx-auto px-4">
        <div className="flex h-16 items-center justify-between">
          <div className="flex items-center space-x-4">
            <Logo />
            <Navigation />
          </div>
          <div className="flex items-center space-x-4">
            <SearchButton />
            <LanguageToggle />
            <ThemeToggle />
            <UserMenu />
          </div>
        </div>
      </div>
    </header>
  );
}
```

### 2. Blog Components

#### PostCard
```typescript
// components/blog/PostCard.tsx
interface PostCardProps {
  post: Post;
  variant?: 'default' | 'featured' | 'compact';
}

export function PostCard({ post, variant = 'default' }: PostCardProps) {
  return (
    <article className={cn(
      "group relative overflow-hidden rounded-lg border bg-card",
      variant === 'featured' && "md:col-span-2",
      variant === 'compact' && "flex space-x-4"
    )}>
      {post.featured_image_url && (
        <div className={cn(
          "aspect-video overflow-hidden",
          variant === 'compact' && "aspect-square w-24 flex-shrink-0"
        )}>
          <Image
            src={post.featured_image_url}
            alt={post.title}
            className="object-cover transition-transform group-hover:scale-105"
            fill
          />
        </div>
      )}
      <div className="p-6">
        <div className="flex items-center space-x-2 text-sm text-muted-foreground mb-2">
          <Badge variant="secondary">{post.categories[0]?.name}</Badge>
          <span>•</span>
          <time>{formatDate(post.published_at)}</time>
        </div>
        <h3 className="font-semibold leading-tight mb-2">
          <Link href={`/posts/${post.slug}`} className="hover:underline">
            {post.title}
          </Link>
        </h3>
        <p className="text-muted-foreground text-sm mb-4 line-clamp-2">
          {post.excerpt}
        </p>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Avatar className="h-6 w-6">
              <AvatarImage src={post.author.avatar_url} />
              <AvatarFallback>{post.author.display_name[0]}</AvatarFallback>
            </Avatar>
            <span className="text-sm text-muted-foreground">
              {post.author.display_name}
            </span>
          </div>
          <div className="flex items-center space-x-4 text-sm text-muted-foreground">
            <span className="flex items-center space-x-1">
              <Eye className="h-4 w-4" />
              <span>{post.view_count}</span>
            </span>
            <span className="flex items-center space-x-1">
              <MessageCircle className="h-4 w-4" />
              <span>{post.comment_count}</span>
            </span>
          </div>
        </div>
      </div>
    </article>
  );
}
```

#### PostContent
```typescript
// components/blog/PostContent.tsx
interface PostContentProps {
  post: Post;
}

export function PostContent({ post }: PostContentProps) {
  return (
    <article className="prose prose-lg dark:prose-invert max-w-none">
      <header className="mb-8">
        <div className="flex items-center space-x-2 text-sm text-muted-foreground mb-4">
          {post.categories.map((category) => (
            <Badge key={category.id} variant="secondary">
              {category.name}
            </Badge>
          ))}
          <span>•</span>
          <time>{formatDate(post.published_at)}</time>
          <span>•</span>
          <span>{post.view_count} views</span>
        </div>
        <h1 className="text-4xl font-bold leading-tight mb-4">{post.title}</h1>
        <div className="flex items-center space-x-4">
          <Avatar>
            <AvatarImage src={post.author.avatar_url} />
            <AvatarFallback>{post.author.display_name[0]}</AvatarFallback>
          </Avatar>
          <div>
            <p className="font-medium">{post.author.display_name}</p>
            <p className="text-sm text-muted-foreground">
              {formatDate(post.published_at)}
            </p>
          </div>
        </div>
      </header>

      {post.featured_image_url && (
        <div className="aspect-video overflow-hidden rounded-lg mb-8">
          <Image
            src={post.featured_image_url}
            alt={post.title}
            className="object-cover"
            fill
          />
        </div>
      )}

      <div className="content">
        <ReactMarkdown
          components={{
            img: ({ src, alt }) => (
              <Image
                src={src || ''}
                alt={alt || ''}
                className="rounded-lg"
                width={800}
                height={400}
              />
            ),
          }}
        >
          {post.content}
        </ReactMarkdown>
      </div>

      <footer className="mt-8 pt-8 border-t">
        <div className="flex flex-wrap gap-2 mb-4">
          {post.tags.map((tag) => (
            <Badge key={tag.id} variant="outline">
              #{tag.name}
            </Badge>
          ))}
        </div>
        <ShareButtons post={post} />
      </footer>
    </article>
  );
}
```

### 3. Admin Components

#### AdminLayout
```typescript
// components/admin/AdminLayout.tsx
export function AdminLayout({ children }: { children: React.ReactNode }) {
  return (
    <div className="flex h-screen bg-background">
      <AdminSidebar />
      <div className="flex-1 flex flex-col overflow-hidden">
        <AdminHeader />
        <main className="flex-1 overflow-auto p-6">
          {children}
        </main>
      </div>
    </div>
  );
}
```

#### PostEditor
```typescript
// components/admin/PostEditor.tsx
interface PostEditorProps {
  post?: Post;
  onSave: (data: PostFormData) => void;
}

export function PostEditor({ post, onSave }: PostEditorProps) {
  const form = useForm<PostFormData>({
    defaultValues: post || {
      title: '',
      content: '',
      excerpt: '',
      status: 'draft',
      categories: [],
      tags: [],
    },
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSave)} className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2 space-y-6">
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Title</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="Enter post title" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="content"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Content</FormLabel>
                  <FormControl>
                    <RichTextEditor {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="excerpt"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Excerpt</FormLabel>
                  <FormControl>
                    <Textarea {...field} placeholder="Brief description" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Publish</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Status</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="draft">Draft</SelectItem>
                          <SelectItem value="published">Published</SelectItem>
                          <SelectItem value="private">Private</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="flex space-x-2">
                  <Button type="submit" variant="default">
                    Save
                  </Button>
                  <Button type="button" variant="outline">
                    Preview
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Categories</CardTitle>
              </CardHeader>
              <CardContent>
                <CategorySelector
                  value={form.watch('categories')}
                  onChange={(categories) => form.setValue('categories', categories)}
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Tags</CardTitle>
              </CardHeader>
              <CardContent>
                <TagSelector
                  value={form.watch('tags')}
                  onChange={(tags) => form.setValue('tags', tags)}
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Featured Image</CardTitle>
              </CardHeader>
              <CardContent>
                <MediaUploader
                  value={form.watch('featured_image_url')}
                  onChange={(url) => form.setValue('featured_image_url', url)}
                />
              </CardContent>
            </Card>
          </div>
        </div>
      </form>
    </Form>
  );
}
```

## State Management

### 1. Zustand Stores

#### Auth Store
```typescript
// stores/authStore.ts
interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  register: (data: RegisterData) => Promise<void>;
}

export const useAuthStore = create<AuthState>((set, get) => ({
  user: null,
  token: null,
  isAuthenticated: false,

  login: async (email, password) => {
    const response = await api.post('/auth/login', { email, password });
    const { user, token } = response.data;

    set({ user, token, isAuthenticated: true });
    localStorage.setItem('token', token);
  },

  logout: () => {
    set({ user: null, token: null, isAuthenticated: false });
    localStorage.removeItem('token');
  },

  register: async (data) => {
    const response = await api.post('/auth/register', data);
    const { user, token } = response.data;

    set({ user, token, isAuthenticated: true });
    localStorage.setItem('token', token);
  },
}));
```

#### UI Store
```typescript
// stores/uiStore.ts
interface UIState {
  sidebarOpen: boolean;
  searchOpen: boolean;
  theme: 'light' | 'dark' | 'system';
  language: 'en' | 'hi';
  toggleSidebar: () => void;
  toggleSearch: () => void;
  setTheme: (theme: 'light' | 'dark' | 'system') => void;
  setLanguage: (language: 'en' | 'hi') => void;
}

export const useUIStore = create<UIState>((set) => ({
  sidebarOpen: false,
  searchOpen: false,
  theme: 'system',
  language: 'en',

  toggleSidebar: () => set((state) => ({ sidebarOpen: !state.sidebarOpen })),
  toggleSearch: () => set((state) => ({ searchOpen: !state.searchOpen })),
  setTheme: (theme) => set({ theme }),
  setLanguage: (language) => set({ language }),
}));
```

### 2. React Query Hooks

#### Posts Hooks
```typescript
// hooks/usePosts.ts
export function usePosts(params?: PostsParams) {
  return useQuery({
    queryKey: ['posts', params],
    queryFn: () => api.get('/posts', { params }),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function usePost(slug: string) {
  return useQuery({
    queryKey: ['post', slug],
    queryFn: () => api.get(`/posts/slug/${slug}`),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

export function useCreatePost() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: PostFormData) => api.post('/posts', data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['posts'] });
    },
  });
}
```

## Routing Strategy

### 1. App Router Structure
```typescript
// app/layout.tsx
export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <Providers>
          <div className="relative flex min-h-screen flex-col">
            {children}
          </div>
        </Providers>
      </body>
    </html>
  );
}

// app/(blog)/layout.tsx
export default function BlogLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <MainLayout>{children}</MainLayout>;
}

// app/(admin)/layout.tsx
export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <AdminLayout>{children}</AdminLayout>;
}
```

### 2. Dynamic Routes
```typescript
// app/(blog)/posts/[slug]/page.tsx
export default async function PostPage({
  params,
}: {
  params: { slug: string };
}) {
  const post = await getPost(params.slug);

  if (!post) {
    notFound();
  }

  return (
    <div className="max-w-4xl mx-auto">
      <PostContent post={post} />
      <CommentSection postId={post.id} />
    </div>
  );
}

// app/(blog)/category/[slug]/page.tsx
export default async function CategoryPage({
  params,
  searchParams,
}: {
  params: { slug: string };
  searchParams: { page?: string };
}) {
  const page = Number(searchParams.page) || 1;
  const { posts, category } = await getPostsByCategory(params.slug, page);

  return (
    <div>
      <CategoryHeader category={category} />
      <PostGrid posts={posts} />
      <Pagination />
    </div>
  );
}
```

## Internationalization

### 1. Configuration
```typescript
// next.config.js
const withNextIntl = require('next-intl/plugin')('./i18n.ts');

module.exports = withNextIntl({
  // Other Next.js config
});

// i18n.ts
import { getRequestConfig } from 'next-intl/server';

export default getRequestConfig(async ({ locale }) => ({
  messages: (await import(`./messages/${locale}.json`)).default,
}));
```

### 2. Usage
```typescript
// components/LanguageToggle.tsx
import { useTranslations } from 'next-intl';

export function LanguageToggle() {
  const t = useTranslations('common');
  const { language, setLanguage } = useUIStore();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm">
          <Globe className="h-4 w-4" />
          <span className="ml-2">{language.toUpperCase()}</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent>
        <DropdownMenuItem onClick={() => setLanguage('en')}>
          English
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setLanguage('hi')}>
          हिंदी
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
```

## Performance Optimization

### 1. Image Optimization
```typescript
// components/OptimizedImage.tsx
interface OptimizedImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  priority?: boolean;
}

export function OptimizedImage({
  src,
  alt,
  width,
  height,
  className,
  priority = false,
}: OptimizedImageProps) {
  return (
    <Image
      src={src}
      alt={alt}
      width={width}
      height={height}
      className={className}
      priority={priority}
      placeholder="blur"
      blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k="
      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
    />
  );
}
```

### 2. Code Splitting
```typescript
// Dynamic imports for heavy components
const RichTextEditor = dynamic(() => import('./RichTextEditor'), {
  loading: () => <div>Loading editor...</div>,
  ssr: false,
});

const AdminDashboard = dynamic(() => import('./AdminDashboard'), {
  loading: () => <DashboardSkeleton />,
});
```

### 3. Caching Strategy
```typescript
// lib/cache.ts
export const revalidate = 3600; // 1 hour

// For static generation
export async function generateStaticParams() {
  const posts = await getPosts({ limit: 100 });
  return posts.map((post) => ({ slug: post.slug }));
}

// For incremental static regeneration
export const dynamic = 'force-static';
export const revalidate = 3600;
```

## SEO Optimization

### 1. Metadata Generation
```typescript
// app/(blog)/posts/[slug]/page.tsx
export async function generateMetadata({
  params,
}: {
  params: { slug: string };
}): Promise<Metadata> {
  const post = await getPost(params.slug);

  if (!post) {
    return {
      title: 'Post Not Found',
    };
  }

  return {
    title: post.meta_title || post.title,
    description: post.meta_description || post.excerpt,
    openGraph: {
      title: post.title,
      description: post.excerpt,
      images: post.featured_image_url ? [post.featured_image_url] : [],
      type: 'article',
      publishedTime: post.published_at,
      authors: [post.author.display_name],
    },
    twitter: {
      card: 'summary_large_image',
      title: post.title,
      description: post.excerpt,
      images: post.featured_image_url ? [post.featured_image_url] : [],
    },
  };
}
```

### 2. Structured Data
```typescript
// components/StructuredData.tsx
export function PostStructuredData({ post }: { post: Post }) {
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'BlogPosting',
    headline: post.title,
    description: post.excerpt,
    image: post.featured_image_url,
    author: {
      '@type': 'Person',
      name: post.author.display_name,
    },
    publisher: {
      '@type': 'Organization',
      name: 'ZayoTech Blog',
    },
    datePublished: post.published_at,
    dateModified: post.updated_at,
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  );
}
```

## Deployment Strategy

### 1. Vercel Configuration
```json
// vercel.json
{
  "framework": "nextjs",
  "buildCommand": "npm run build",
  "devCommand": "npm run dev",
  "installCommand": "npm install",
  "functions": {
    "app/api/**/*.ts": {
      "runtime": "nodejs18.x"
    }
  },
  "env": {
    "NEXT_PUBLIC_API_URL": "@api-url",
    "NEXT_PUBLIC_MEDIA_URL": "@media-url"
  }
}
```

### 2. Environment Variables
```bash
# .env.local
NEXT_PUBLIC_API_URL=https://api.yourblog.com/v1
NEXT_PUBLIC_MEDIA_URL=https://media.yourblog.com
NEXT_PUBLIC_SITE_URL=https://yourblog.com

# Private variables
API_SECRET_KEY=your-secret-key
DATABASE_URL=your-database-url
```

This frontend architecture provides a modern, scalable, and maintainable foundation for your blog website with excellent performance, SEO optimization, and user experience.
```