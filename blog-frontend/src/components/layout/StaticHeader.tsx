export function StaticHeader() {
  return (
    <header>
      <div className="container">
        <div className="header-content">
          <div className="flex items-center space-x-6">
            <a href="/" className="logo">
              ZayoTech Blog
            </a>
            <nav className="nav-menu">
              <a href="/category/quotes/" className="nav-link">
                Quotes
              </a>
              <a href="/category/shayari/" className="nav-link">
                <PERSON><PERSON>
              </a>
              <a href="/category/stories/" className="nav-link">
                Stories
              </a>
              <a href="/category/hindi/" className="nav-link">
                हिंदी
              </a>
              <a href="/category/english/" className="nav-link">
                English
              </a>
            </nav>
          </div>
          
          <div className="flex items-center space-x-4">
            {/* Search button - enhanced progressively with JavaScript */}
            <button 
              className="search-toggle p-2 rounded-md hover:bg-gray-100 transition-colors"
              data-search-toggle
              aria-label="Search"
            >
              <svg 
                className="w-5 h-5" 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
                aria-hidden="true"
              >
                <path 
                  strokeLinecap="round" 
                  strokeLinejoin="round" 
                  strokeWidth={2} 
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" 
                />
              </svg>
            </button>
            
            {/* Language toggle */}
            <div className="language-toggle">
              <a 
                href="?lang=hi" 
                className="text-sm text-gray-600 hover:text-blue-600 transition-colors"
                data-lang="hi"
              >
                हिं
              </a>
              <span className="text-gray-400 mx-1">|</span>
              <a 
                href="?lang=en" 
                className="text-sm text-gray-600 hover:text-blue-600 transition-colors"
                data-lang="en"
              >
                EN
              </a>
            </div>
            
            {/* Mobile menu button */}
            <button 
              className="mobile-menu-toggle md:hidden p-2 rounded-md hover:bg-gray-100 transition-colors"
              data-mobile-menu-toggle
              aria-label="Toggle menu"
            >
              <svg 
                className="w-5 h-5" 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
                aria-hidden="true"
              >
                <path 
                  strokeLinecap="round" 
                  strokeLinejoin="round" 
                  strokeWidth={2} 
                  d="M4 6h16M4 12h16M4 18h16" 
                />
              </svg>
            </button>
          </div>
        </div>
        
        {/* Mobile menu - hidden by default, shown with JavaScript */}
        <nav 
          className="mobile-menu hidden md:hidden py-4 border-t border-gray-200"
          data-mobile-menu
        >
          <div className="flex flex-col space-y-3">
            <a href="/category/quotes/" className="nav-link">
              Quotes
            </a>
            <a href="/category/shayari/" className="nav-link">
              Shayari
            </a>
            <a href="/category/stories/" className="nav-link">
              Stories
            </a>
            <a href="/category/hindi/" className="nav-link">
              हिंदी
            </a>
            <a href="/category/english/" className="nav-link">
              English
            </a>
          </div>
        </nav>
        
        {/* Search overlay - enhanced with JavaScript */}
        <div 
          className="search-overlay hidden fixed inset-0 bg-black bg-opacity-50 z-50"
          data-search-overlay
        >
          <div className="search-modal bg-white rounded-lg shadow-xl max-w-2xl mx-auto mt-20 p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold">Search Blog</h2>
              <button 
                className="search-close p-2 hover:bg-gray-100 rounded-md"
                data-search-close
                aria-label="Close search"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <form action="/search/" method="GET" className="search-form">
              <input
                type="text"
                name="q"
                placeholder="Search for quotes, shayari, stories..."
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                data-search-input
              />
              <button 
                type="submit"
                className="mt-3 w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Search
              </button>
            </form>
          </div>
        </div>
      </div>
    </header>
  );
}
