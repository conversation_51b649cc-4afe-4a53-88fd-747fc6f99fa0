export function StaticFooter() {
  const currentYear = new Date().getFullYear();
  
  return (
    <footer className="bg-gray-50 border-t border-gray-200 mt-16">
      <div className="container">
        <div className="py-12">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {/* Brand section */}
            <div className="md:col-span-2">
              <a href="/" className="logo text-xl font-bold text-gray-900 mb-4 block">
                ZayoTech Blog
              </a>
              <p className="text-gray-600 text-sm leading-relaxed mb-4">
                Discover inspiring quotes, beautiful shayari, and meaningful stories. 
                A collection of wisdom and emotions in Hindi and English to brighten your day.
              </p>
              <div className="flex space-x-4">
                <a 
                  href="#" 
                  className="text-gray-400 hover:text-blue-600 transition-colors"
                  aria-label="Facebook"
                >
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                  </svg>
                </a>
                <a 
                  href="#" 
                  className="text-gray-400 hover:text-blue-600 transition-colors"
                  aria-label="Twitter"
                >
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                  </svg>
                </a>
                <a 
                  href="#" 
                  className="text-gray-400 hover:text-blue-600 transition-colors"
                  aria-label="Instagram"
                >
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.297-3.323C5.902 8.198 7.053 7.708 8.35 7.708s2.448.49 3.323 1.297c.897.875 1.387 2.026 1.387 3.323s-.49 2.448-1.297 3.323c-.875.897-2.026 1.387-3.323 1.387z"/>
                  </svg>
                </a>
              </div>
            </div>
            
            {/* Categories */}
            <div>
              <h3 className="font-semibold text-gray-900 mb-4">Categories</h3>
              <ul className="space-y-2">
                <li>
                  <a href="/category/quotes/" className="text-gray-600 hover:text-blue-600 transition-colors text-sm">
                    Quotes
                  </a>
                </li>
                <li>
                  <a href="/category/shayari/" className="text-gray-600 hover:text-blue-600 transition-colors text-sm">
                    Shayari
                  </a>
                </li>
                <li>
                  <a href="/category/stories/" className="text-gray-600 hover:text-blue-600 transition-colors text-sm">
                    Stories
                  </a>
                </li>
                <li>
                  <a href="/category/hindi/" className="text-gray-600 hover:text-blue-600 transition-colors text-sm">
                    Hindi Content
                  </a>
                </li>
                <li>
                  <a href="/category/english/" className="text-gray-600 hover:text-blue-600 transition-colors text-sm">
                    English Content
                  </a>
                </li>
              </ul>
            </div>
            
            {/* Quick Links */}
            <div>
              <h3 className="font-semibold text-gray-900 mb-4">Quick Links</h3>
              <ul className="space-y-2">
                <li>
                  <a href="/about/" className="text-gray-600 hover:text-blue-600 transition-colors text-sm">
                    About Us
                  </a>
                </li>
                <li>
                  <a href="/contact/" className="text-gray-600 hover:text-blue-600 transition-colors text-sm">
                    Contact
                  </a>
                </li>
                <li>
                  <a href="/privacy/" className="text-gray-600 hover:text-blue-600 transition-colors text-sm">
                    Privacy Policy
                  </a>
                </li>
                <li>
                  <a href="/terms/" className="text-gray-600 hover:text-blue-600 transition-colors text-sm">
                    Terms of Service
                  </a>
                </li>
                <li>
                  <a href="/sitemap.xml" className="text-gray-600 hover:text-blue-600 transition-colors text-sm">
                    Sitemap
                  </a>
                </li>
              </ul>
            </div>
          </div>
        </div>
        
        {/* Bottom bar */}
        <div className="py-6 border-t border-gray-200">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-600 text-sm">
              © {currentYear} ZayoTech Blog. All rights reserved.
            </p>
            <div className="flex items-center space-x-4 mt-4 md:mt-0">
              <span className="text-gray-500 text-xs">
                Made with ❤️ for sharing wisdom
              </span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
