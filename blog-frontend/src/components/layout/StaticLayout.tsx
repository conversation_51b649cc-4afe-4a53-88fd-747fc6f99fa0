import { ReactNode } from 'react';
import { St<PERSON>Header } from './StaticHeader';
import { StaticFooter } from './StaticFooter';

interface StaticLayoutProps {
  children: ReactNode;
  title: string;
  description: string;
  canonical?: string;
  ogImage?: string;
  jsonLd?: object;
}

export function StaticLayout({ 
  children, 
  title, 
  description, 
  canonical,
  ogImage = '/images/og-default.jpg',
  jsonLd 
}: StaticLayoutProps) {
  const fullTitle = title === 'ZayoTech Blog' ? title : `${title} | ZayoTech Blog`;
  const siteUrl = 'https://yourblog.com';
  const fullCanonical = canonical || siteUrl;

  return (
    <html lang="en">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <title>{fullTitle}</title>
        <meta name="description" content={description} />
        <link rel="canonical" href={fullCanonical} />
        
        {/* Open Graph / Facebook */}
        <meta property="og:type" content="website" />
        <meta property="og:url" content={fullCanonical} />
        <meta property="og:title" content={fullTitle} />
        <meta property="og:description" content={description} />
        <meta property="og:image" content={`${siteUrl}${ogImage}`} />
        <meta property="og:site_name" content="ZayoTech Blog" />
        
        {/* Twitter */}
        <meta property="twitter:card" content="summary_large_image" />
        <meta property="twitter:url" content={fullCanonical} />
        <meta property="twitter:title" content={fullTitle} />
        <meta property="twitter:description" content={description} />
        <meta property="twitter:image" content={`${siteUrl}${ogImage}`} />
        
        {/* Favicon */}
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
        
        {/* Preload critical resources */}
        <link rel="preload" href="/fonts/inter-var.woff2" as="font" type="font/woff2" crossOrigin="" />
        <link rel="preconnect" href="https://media.yourblog.com" />
        
        {/* Critical CSS - inlined for maximum performance */}
        <style dangerouslySetInnerHTML={{ 
          __html: `
            /* Critical CSS will be inlined here during build */
            /* For now, using basic styles */
            body { font-family: system-ui, sans-serif; margin: 0; line-height: 1.6; }
            .container { max-width: 1200px; margin: 0 auto; padding: 0 1rem; }
            header { border-bottom: 1px solid #e5e7eb; background: white; position: sticky; top: 0; z-index: 50; }
            .header-content { display: flex; align-items: center; justify-content: space-between; height: 4rem; }
            .logo { font-size: 1.25rem; font-weight: 700; color: #1f2937; text-decoration: none; }
            .nav-menu { display: none; align-items: center; gap: 1.5rem; }
            @media (min-width: 768px) { .nav-menu { display: flex; } }
            .nav-link { color: #6b7280; text-decoration: none; font-weight: 500; }
            .nav-link:hover { color: #2563eb; }
            main { min-height: calc(100vh - 4rem); padding: 2rem 0; }
            .grid { display: grid; gap: 1.5rem; }
            .grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
            @media (min-width: 768px) { .grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); } }
            @media (min-width: 1024px) { .grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); } }
            .card { background: white; border-radius: 0.5rem; border: 1px solid #e5e7eb; overflow: hidden; }
            .card:hover { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); }
            .card-image { width: 100%; height: 12rem; object-fit: cover; }
            .card-content { padding: 1.5rem; }
            .card-title { font-size: 1.125rem; font-weight: 600; margin: 0 0 0.5rem 0; }
            .card-title a { color: #1f2937; text-decoration: none; }
            .card-title a:hover { color: #2563eb; }
            .sr-only { position: absolute; width: 1px; height: 1px; padding: 0; margin: -1px; overflow: hidden; clip: rect(0, 0, 0, 0); }
          `
        }} />
        
        {/* Non-critical CSS loaded asynchronously */}
        <link 
          rel="preload" 
          href="/styles/main.css" 
          as="style" 
          onLoad="this.onload=null;this.rel='stylesheet'" 
        />
        <noscript>
          <link rel="stylesheet" href="/styles/main.css" />
        </noscript>
        
        {/* JSON-LD structured data */}
        {jsonLd && (
          <script
            type="application/ld+json"
            dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
          />
        )}
      </head>
      <body>
        <StaticHeader />
        <main>
          {children}
        </main>
        <StaticFooter />
        
        {/* Progressive enhancement script loaded asynchronously */}
        <script type="module" src="/js/progressive-enhancement.js" async />
      </body>
    </html>
  );
}
