interface Post {
  id: number;
  title: string;
  slug: string;
  excerpt: string;
  featured_image_url?: string;
  published_at: string;
  view_count: number;
  comment_count: number;
  author: {
    display_name: string;
    avatar_url?: string;
  };
  categories: Array<{
    name: string;
    slug: string;
  }>;
  language: 'en' | 'hi';
}

interface StaticPostCardProps {
  post: Post;
  variant?: 'default' | 'featured' | 'compact';
  showImage?: boolean;
}

export function StaticPostCard({ 
  post, 
  variant = 'default',
  showImage = true 
}: StaticPostCardProps) {
  const cardClasses = [
    "card group relative overflow-hidden transition-all duration-200",
    variant === 'featured' && "md:col-span-2",
    variant === 'compact' && "flex space-x-4"
  ].filter(Boolean).join(' ');

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric' 
    });
  };

  const getOptimizedImageUrl = (url: string, width: number = 600, height: number = 400) => {
    if (!url) return '';
    // Use Cloudflare Image Optimization
    return `https://media.yourblog.com/cdn-cgi/image/width=${width},height=${height},format=webp,quality=85/${url}`;
  };

  return (
    <article className={cardClasses}>
      {showImage && post.featured_image_url && (
        <div className={
          variant === 'compact' 
            ? "aspect-square w-24 flex-shrink-0 overflow-hidden rounded-md" 
            : "aspect-video overflow-hidden"
        }>
          <img
            src={getOptimizedImageUrl(post.featured_image_url)}
            alt={post.title}
            className="card-image w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
            loading="lazy"
            decoding="async"
            width="600"
            height="400"
          />
        </div>
      )}
      
      <div className="card-content">
        {/* Meta information */}
        <div className="flex items-center space-x-2 text-sm text-gray-600 mb-2">
          {post.categories.length > 0 && (
            <a 
              href={`/category/${post.categories[0].slug}/`}
              className="inline-block px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium hover:bg-blue-200 transition-colors"
            >
              {post.categories[0].name}
            </a>
          )}
          
          {post.language === 'hi' && (
            <span className="inline-block px-2 py-1 bg-orange-100 text-orange-800 rounded-full text-xs font-medium">
              हिंदी
            </span>
          )}
          
          <span className="text-gray-400">•</span>
          
          <time 
            dateTime={post.published_at}
            className="text-gray-500"
          >
            {formatDate(post.published_at)}
          </time>
        </div>

        {/* Title */}
        <h3 className="card-title">
          <a 
            href={`/posts/${post.slug}/`} 
            className="hover:text-blue-600 transition-colors duration-200"
          >
            {post.title}
          </a>
        </h3>

        {/* Excerpt */}
        {post.excerpt && (
          <p className="card-excerpt text-gray-600 text-sm line-clamp-2 mb-4">
            {post.excerpt}
          </p>
        )}

        {/* Footer with author and stats */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {post.author.avatar_url ? (
              <img
                src={post.author.avatar_url}
                alt={post.author.display_name}
                className="w-6 h-6 rounded-full object-cover"
                loading="lazy"
                width="24"
                height="24"
              />
            ) : (
              <div className="w-6 h-6 rounded-full bg-gray-300 flex items-center justify-center text-xs font-medium text-gray-600">
                {post.author.display_name.charAt(0).toUpperCase()}
              </div>
            )}
            <span className="text-sm text-gray-600">
              {post.author.display_name}
            </span>
          </div>
          
          <div className="flex items-center space-x-4 text-sm text-gray-500">
            <span className="flex items-center space-x-1" title="Views">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
              <span>{post.view_count.toLocaleString()}</span>
            </span>
            
            <span className="flex items-center space-x-1" title="Comments">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
              <span>{post.comment_count}</span>
            </span>
          </div>
        </div>
      </div>
    </article>
  );
}
