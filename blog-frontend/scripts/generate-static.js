const fs = require('fs');
const path = require('path');

async function generateStaticSite() {
  console.log('🚀 Starting static site generation...');
  
  // For now, we'll create a basic structure
  // Later this will fetch from the actual API
  const posts = [
    {
      slug: 'welcome-to-our-blog',
      title: 'Welcome to Our Blog',
      updated_at: new Date().toISOString(),
      category: 'general'
    },
    {
      slug: 'inspirational-quotes',
      title: 'Daily Inspirational Quotes',
      updated_at: new Date().toISOString(),
      category: 'quotes'
    },
    {
      slug: 'beautiful-shayari',
      title: 'Beautiful Shayari Collection',
      updated_at: new Date().toISOString(),
      category: 'shayari'
    }
  ];
  
  // Generate static paths
  const staticPaths = {
    '/': { priority: 1.0, changefreq: 'daily' },
    '/about/': { priority: 0.8, changefreq: 'monthly' },
    '/contact/': { priority: 0.6, changefreq: 'monthly' },
    '/category/quotes/': { priority: 0.9, changefreq: 'weekly' },
    '/category/shayari/': { priority: 0.9, changefreq: 'weekly' },
    '/category/hindi/': { priority: 0.9, changefreq: 'weekly' },
    '/category/english/': { priority: 0.9, changefreq: 'weekly' }
  };

  // Add post paths
  posts.forEach(post => {
    staticPaths[`/posts/${post.slug}/`] = {
      priority: 0.9,
      changefreq: 'weekly',
      lastmod: post.updated_at
    };
  });

  // Generate sitemap
  const sitemap = generateSitemap(staticPaths);
  
  // Ensure public directory exists
  if (!fs.existsSync('public')) {
    fs.mkdirSync('public', { recursive: true });
  }
  
  fs.writeFileSync('public/sitemap.xml', sitemap);

  // Generate robots.txt
  const robotsTxt = `User-agent: *
Allow: /

Sitemap: https://yourblog.com/sitemap.xml`;
  
  fs.writeFileSync('public/robots.txt', robotsTxt);

  console.log(`✅ Generated ${Object.keys(staticPaths).length} static pages`);
  console.log('✅ Generated sitemap.xml and robots.txt');
}

function generateSitemap(paths) {
  const urls = Object.entries(paths).map(([path, meta]) => `
    <url>
      <loc>https://yourblog.com${path}</loc>
      <lastmod>${meta.lastmod || new Date().toISOString()}</lastmod>
      <changefreq>${meta.changefreq}</changefreq>
      <priority>${meta.priority}</priority>
    </url>
  `).join('');

  return `<?xml version="1.0" encoding="UTF-8"?>
    <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
      ${urls}
    </urlset>`;
}

// Run if called directly
if (require.main === module) {
  generateStaticSite().catch(console.error);
}

module.exports = { generateStaticSite };
