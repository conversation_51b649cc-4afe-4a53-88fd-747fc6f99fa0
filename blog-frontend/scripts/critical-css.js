const critical = require('critical');
const fs = require('fs');
const path = require('path');

async function extractCriticalCSS() {
  console.log('🎨 Starting critical CSS extraction...');
  
  // Ensure directories exist
  const criticalDir = 'src/styles/critical';
  if (!fs.existsSync(criticalDir)) {
    fs.mkdirSync(criticalDir, { recursive: true });
  }

  // Pages to extract critical CSS for
  const pages = [
    { 
      url: 'http://localhost:3000/', 
      output: 'home',
      description: 'Homepage critical CSS'
    },
    { 
      url: 'http://localhost:3000/posts/sample-post/', 
      output: 'post',
      description: 'Blog post critical CSS'
    },
    { 
      url: 'http://localhost:3000/category/quotes/', 
      output: 'category',
      description: 'Category page critical CSS'
    }
  ];

  console.log('ℹ️ Note: This requires the development server to be running');
  console.log('ℹ️ Run "npm run dev" in another terminal first');

  for (const page of pages) {
    try {
      console.log(`🔄 Extracting critical CSS for ${page.description}...`);
      
      const { css } = await critical.generate({
        base: 'out/',
        src: page.url,
        width: 1300,
        height: 900,
        inline: false,
        extract: false, // Don't remove from original CSS
        ignore: {
          atrule: ['@font-face'],
          rule: [/\.hidden/, /\.sr-only/],
          decl: (node, value) => /url\(/.test(value)
        },
        penthouse: {
          timeout: 30000,
          pageLoadSkipTimeout: 5000
        }
      });

      const outputPath = path.join(criticalDir, `${page.output}.css`);
      fs.writeFileSync(outputPath, css);
      
      console.log(`✅ Generated ${outputPath}`);
      
    } catch (error) {
      console.warn(`⚠️ Could not extract critical CSS for ${page.output}:`, error.message);
      
      // Create a fallback critical CSS file
      const fallbackCSS = `/* Fallback critical CSS for ${page.output} */
/* Add your critical styles here manually */
body {
  font-family: system-ui, -apple-system, sans-serif;
  line-height: 1.6;
  margin: 0;
  padding: 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

header {
  border-bottom: 1px solid #e5e7eb;
  background: white;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}`;
      
      const outputPath = path.join(criticalDir, `${page.output}.css`);
      fs.writeFileSync(outputPath, fallbackCSS);
      console.log(`📝 Created fallback CSS at ${outputPath}`);
    }
  }

  // Create a combined critical CSS file
  const criticalFiles = fs.readdirSync(criticalDir)
    .filter(file => file.endsWith('.css'));
  
  let combinedCSS = '/* Combined Critical CSS */\n';
  
  criticalFiles.forEach(file => {
    const content = fs.readFileSync(path.join(criticalDir, file), 'utf8');
    combinedCSS += `\n/* From ${file} */\n${content}\n`;
  });
  
  fs.writeFileSync(path.join(criticalDir, 'combined.css'), combinedCSS);
  
  console.log('✅ Critical CSS extraction completed');
  console.log(`📁 Files saved in: ${criticalDir}`);
}

// Run if called directly
if (require.main === module) {
  extractCriticalCSS().catch(console.error);
}

module.exports = { extractCriticalCSS };
